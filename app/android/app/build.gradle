apply plugin: "com.android.application"
apply plugin: "com.facebook.react"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    reactNativeDir = file("../../../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    codegenDir = file("../../../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    cliFile = file("../../../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    hermesCommand = "$rootDir/../../node_modules/react-native/sdks/hermesc/%OS-BIN%/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    ndkVersion rootProject.ext.ndkVersion

    compileSdkVersion rootProject.ext.compileSdkVersion

    buildFeatures {
        buildConfig true
    }

    namespace "com.aml.evapp"
    defaultConfig {
        applicationId "com.aml.evapp"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        // Sets version code as specified in build params and converts from string to int, or defaults to 1
        versionCode project.properties['versionCode'] ? Integer.parseInt(project.properties['versionCode']) : 1
        versionName project.properties['versionName'] ?: "4.1.0"
        // react-native-config
        resValue "string", "build_config_package", "com.aml.evapp"
    }
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file(MYAPP_UPLOAD_STORE_FILE)
            storePassword MYAPP_UPLOAD_STORE_PASSWORD
            keyAlias MYAPP_UPLOAD_KEY_ALIAS
            keyPassword MYAPP_UPLOAD_KEY_PASSWORD
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            // Caution! In production, you need to generate your own keystore file.
            // see https://reactnative.dev/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"

            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            }
        }
    }

    flavorDimensions "version"
    productFlavors {
        local {
            applicationIdSuffix ".debug"
            signingConfig signingConfigs.debug
            resValue "string", "app_name", "bp pulse"
        }
        localAral {
            applicationIdSuffix "aral.debug"
            signingConfig signingConfigs.debug
            resValue "string", "app_name", "Aral pulse"
        }
        localUS {
            applicationIdSuffix "us.debug"
            signingConfig signingConfigs.debug
            resValue "string", "app_name", "bp pulse"
        }
        dev {
            applicationIdSuffix ".debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        devAral {
            applicationIdSuffix "aral.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "Aral pulse"
        }
        devUS {
            applicationIdSuffix "us.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        qa {
            applicationIdSuffix ".debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        qaAral {
            applicationIdSuffix "aral.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "Aral pulse"
        }
        qaUS {
            applicationIdSuffix "us.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        performance {
            applicationIdSuffix ".debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        performanceAral {
            applicationIdSuffix "aral.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "Aral pulse"
        }
        performanceUS {
            applicationIdSuffix "us.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        preprod {
            applicationIdSuffix ".debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        preprodAral {
            applicationIdSuffix "aral.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "Aral pulse"
        }
        preprodUS {
            applicationIdSuffix "us.debug"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        prod {
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
        prodAral {
            applicationIdSuffix "aral"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "Aral pulse"
        }
        prodUS {
            applicationId "com.bppulse.us"
            signingConfig signingConfigs.release
            resValue "string", "app_name", "bp pulse"
        }
    }
}

repositories {
  maven {
    url  "https://cardinalcommerceprod.jfrog.io/artifactory/android"
      credentials {
      username artifactory_username
      password artifactory_password
    }
  }
}

// Force using stable versions for problematic dependencies
configurations.all {
    resolutionStrategy {
        force "androidx.appcompat:appcompat:1.6.1"
        force "androidx.appcompat:appcompat-resources:1.6.1"
        force "androidx.lifecycle:lifecycle-livedata-core:2.6.2"
        force "androidx.lifecycle:lifecycle-runtime:2.6.2"
        force "androidx.lifecycle:lifecycle-common:2.6.2"
        force "androidx.lifecycle:lifecycle-viewmodel:2.6.2"
        force "androidx.fragment:fragment:1.6.2"
        force "androidx.concurrent:concurrent-futures:1.1.0"
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")

    // Cardinal
    implementation 'org.jfrog.cardinalcommerce.gradle:cardinalmobilesdk:2.2.7-6'
    // react-native-inappbrowser-reborn
    implementation project(':react-native-inappbrowser-reborn')




    // react-native-bootsplash
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"

    debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}")
    debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        exclude group:'com.squareup.okhttp3', module:'okhttp'
    }

    debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}")
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

apply from: file("../../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
