import {
  HelpPageAnalyticsEvent,
  LoginAnalyticsEvent,
  MigrationFlowAnalyticsEvent,
  OutageAnalyticsEvent,
  SiteBannerAnalyticsEvent,
  TabsAnalyticsEvent,
  UberAnalyticsEvent,
} from '@analytics/enums';
import { WalletAnalyticsEvent } from '@bp/bppay-wallet-feature';
import { ChargeHistoryAnalyticsEvent } from '@bp/charge-history-mfe';
import { ChargeAnalyticsEvent } from '@bp/charge-mfe';
import { CreditAnalyticsEvent } from '@bp/credit-mfe';
import { FavouritesAnalyticsEvent } from '@bp/favourites-mfe';
import { GuestAnalyticsEvent } from '@bp/guest_feature-mfe';
import { MapAnalyticsEvent } from '@bp/map-mfe';
import { SubsAnalyticsEvent } from '@bp/mfe-subscription';
import { OnboardingAnalyticsEvent } from '@bp/onboarding-mfe';
import { PartnerDriverAnalyticsEvent } from '@bp/partnerdriver-mfe/dist/analytics';
import { ProfileAnalyticsEvent } from '@bp/profile-mfe';
import { LoginAnalyticsEvent as AuthAnalyticsEventsType } from '@bp/pulse-auth-sdk';
import { RegAnalyticsEvent } from '@bp/registration-mfe';
import { RFIDAnalyticsEvent, RFIDLinkingConfig } from '@bp/rfid-mfe';
import { RTBFAnalyticsEvent } from '@bp/rtbf-mfe';
import { getUserInfo } from '@common/asyncStorage';
import env from '@env';
import Airship, { CustomEvent, EventType } from '@ua/react-native-airship';
import { logger } from '@utils/logger';
import { navigate } from '@utils/navigation';
import { Platform } from 'react-native';

import type { AnalyticsEventMap, AnalyticsEventType } from '../analytics.types';

export const logCustomEvent = async (
  eventType: string,
  payload?: Record<string, any>,
) => {
  try {
    logger.debug('Airship ++++++++++++= eventType', eventType);
    const userInfo = await getUserInfo();
    const customEvent = AirshipAnalyticsService.createCustomEvent(
      eventType.trim(),
      payload,
      userInfo,
    );
    logger.debug('JSON data', JSON.stringify(customEvent));
    // Log the custom event with Airship
    await Airship.analytics.addCustomEvent(customEvent);
  } catch (e) {
    logger.error('There was an error calling Airship logCustomEvent: ', e);
  }
};

const eventMap: AnalyticsEventMap = {
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATION_SCREEN_OPEN]: undefined,
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_DISMISS_CLICK]: undefined,
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_UPDATEACCOUNT_CLICK]:
    undefined,
  [MigrationFlowAnalyticsEvent.SUBS_MIGRATIONSCREEN_KEEPSUBS_CLICK]: undefined,

  [MapAnalyticsEvent.SEARCH_BAR_FOCUS]: undefined,
  [MapAnalyticsEvent.SEARCH_TEXT_CHANGE]: undefined,
  [MapAnalyticsEvent.SEARCH_LOCATION_SELECT]: undefined,
  [MapAnalyticsEvent.SEARCH_HISTORY_LOCATION_SELECT]: undefined,
  [MapAnalyticsEvent.SITE_DETAILS_CLOSE]: undefined,
  [MapAnalyticsEvent.SITE_DIRECTIONS_VIEW]: undefined,
  [MapAnalyticsEvent.SITE_FAVOURITE_ADD]: undefined,
  [MapAnalyticsEvent.SITE_FAVOURITE_REMOVE]: undefined,
  [MapAnalyticsEvent.SITE_MARKER_SELECT]: () => {
    return logCustomEvent('map_site_marker_click');
  },
  [MapAnalyticsEvent.SITE_DETAILS_VIEW]: undefined,
  [MapAnalyticsEvent.NEARBY_SITES_OPEN]: undefined,
  [MapAnalyticsEvent.NEARBY_SITE_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_CONNECTOR_TYPE_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_CONNECTOR_TYPE_SAVE]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_SPEED_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_SPEED_SAVE]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_FAVOURITES_SELECT]: undefined,
  [MapAnalyticsEvent.QUICK_FILTER_SHOW_PULSE_ONLY_SELECT]: undefined,
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_SELECT]: undefined,
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_OPEN]: undefined,
  [MapAnalyticsEvent.LOGIN_TO_SHOW_FAVOURITES_CLOSE]: undefined,
  [MapAnalyticsEvent.CHARGEPOINT_CHARGE_START]: () => {
    return logCustomEvent('map_cp_charge_now_click');
  },
  [MapAnalyticsEvent.CHARGEPOINT_CHARGE_AS_GUEST_SELECT]: () => {
    return logCustomEvent('map_cp_charge_as_a_guest_click');
  },
  [MapAnalyticsEvent.FILTER_PAGE_OPEN]: undefined,
  [MapAnalyticsEvent.FILTER_PAGE_SAVE]: undefined,
  [MapAnalyticsEvent.FILTER_PAGE_CLOSE]: undefined,
  [MapAnalyticsEvent.FILTER_PAGE_CLEAR]: undefined,
  [MapAnalyticsEvent.MAP_RECENTER_SELECT]: undefined,
  [MapAnalyticsEvent.MAP_SCREEN_OPEN]: () => {
    return logCustomEvent('map-map-open');
  },
  [MapAnalyticsEvent.LOCATION_SERVICE_DISABLED_OPEN]: undefined,
  [MapAnalyticsEvent.LOCATION_SERVICE_DISABLED_CLOSE]: undefined,
  [MapAnalyticsEvent.OPERATOR_FILTERS_SAVE]: undefined,

  [PartnerDriverAnalyticsEvent.PARTNER_OFFERS_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_BACK]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_CONTINUE]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_ID_ENTRY_SCREEN_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LINK_RESULT_SCREEN_CTA_CLICK]: undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_ADAC_LINK_RESULT_SCREEN_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_BACK]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_GET_RFID]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_LEARN_MORE]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_MEMBERSHIP_DETAILS_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_RETRY]: undefined,
  [PartnerDriverAnalyticsEvent.ADAC_LOADING_FAILED_SCREEN_BACK]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_ORDER_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_LOGOUT_MODAL_LOGOUT_CLICK]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_SETUP_ORDER_CHARGE_CARD_LOGOUT_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_EXPLORE_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.PARTNER_UBER_OFFER_SCREEN_UNLINK_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UNLINK_ACCOUNTS_SUCCESS_SUBSCRIBE_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UNLINK_ACCOUNTS_SUCCESS_FIND_CHARGER_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_MODAL_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_KEEP_UBER_CLICK]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_EVENT]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_OPEN]: undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_TRY_AGAIN_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_UNSUCCESSFULL_CONTACT_CLICK]:
    undefined,
  [PartnerDriverAnalyticsEvent.UBER_UNLINK_ACCOUNTS_SUCCESSFULL_OPEN]:
    undefined,

  [ProfileAnalyticsEvent.PROFILE_SCREEN_LOGOUT_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_LOGIN_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_REGISTER_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_LANGUAGE_SELECTOR_UPDATE]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_FEEDBACK_CLICK]: () => {
    return logCustomEvent('profile_profile_screen_feedback_click');
  },
  [ProfileAnalyticsEvent.SETTINGS_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.ACCOUNT_DETAILS_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_OPEN]: undefined,
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_UPDATE_SAVE]: undefined,
  [ProfileAnalyticsEvent.MARKETING_PREFERENCE_OPT_OUT_SAVE]: undefined,
  [ProfileAnalyticsEvent.PROFILE_SCREEN_OPEN]: () => {
    return logCustomEvent('profile_profile_screen_open');
  },
  [ProfileAnalyticsEvent.SETTINGS_SCREEN_TCS_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_EDIT_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_DONE_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_ADD_ADDRESS_CLICK]:
    undefined,
  [ProfileAnalyticsEvent.PROFILE_PERSONAL_INFORMATION_UPDATE_ADDRESS_CLICK]:
    undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_MANUAL_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_SEARCH_ERROR]: undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_BACK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_EXIT_MODAL_DELETE_CHANGES_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_EXIT_MODAL_KEEP_EDITING_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_ADD_ADDRESS_SCREEN_SAVE_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_SUCCESSFUL_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_SUCCESSFUL_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_FAILED_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DEATILS_FAIL_TRY_AGAIN_CLICK]:
    undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_DETAILS_FAIL_EXIT_CLICK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SCREEN_BACK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SUCCESS_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_SUCCESS_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_FAILURE_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_NAME_FAILURE_FINISH]: undefined,

  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SCREEN_BACK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_VERIFY_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SUCCESS_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_SUCCESS_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_FAILURE_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_EMAIL_FAILURE_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SCREEN_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SCREEN_BACK]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_VERIFY_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_VERIFY_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SUCCESS_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_SUCCESS_FINISH]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_FAILURE_OPEN]: undefined,
  [ProfileAnalyticsEvent.PROFILE_UPDATE_MOBILE_FAILURE_FINISH]: undefined,

  [HelpPageAnalyticsEvent.HELP_SCREEN_EMAIL_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_SCREEN_PHONE_NUMBER_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_SCREEN_FAQS_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_WEB_FORM_CLICK]: undefined,
  [HelpPageAnalyticsEvent.HELP_PARTNER_SITE_CLICK]: undefined,

  // Charge History Analytics
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_OPEN]: undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_CLICK]: () => {
    return logCustomEvent('history_activity_click');
  },
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADVAT_CLICK]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADVAT_FAILED]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_ADDADDRESS]: undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITY_UNPAIDVIEW_MAKEPAYMENT]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADCREDITNOTE]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_DOWNLOADORIGINAL]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_CREDITNOTE_FAILED]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_ACTIVITYVIEW_REFUND_ADDADDRESS]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_CLICK]: () => {
    return logCustomEvent('history_subs_invoice_click');
  },
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_CHARGINGFEES_CLICK]:
    undefined,

  [GuestAnalyticsEvent.PRE_AUTHORISATION_FAILED]: undefined,
  [GuestAnalyticsEvent.PRE_AUTHORISATION_SUCCESSFUL]: undefined,

  [RFIDAnalyticsEvent.PULSE_CHARGE_CARD_OPEN]: undefined,
  [RFIDAnalyticsEvent.CONFIRM_AND_ORDER_ORDER_CTA_CLICK]: undefined,
  [RFIDAnalyticsEvent.CARD_ORDER_SUCCESS]: () => {
    return logCustomEvent('rfid_card_order_success');
  },
  [RFIDAnalyticsEvent.CARD_ORDER_FAILED]: undefined,
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_REPLACE_CLICK]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_POP_UP_ORDER_NEW_SELECT]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_POP_UP_KEEP_CARD_SELECT]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_SUCCESS]: undefined,
  [RFIDAnalyticsEvent.REPLACE_CARD_FAILED]: undefined,
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_CANCEL_CLICK]: undefined,
  [RFIDAnalyticsEvent.CANCEL_CARD_FAILED]: undefined,
  [RFIDAnalyticsEvent.PULSE_CHARGE_CARD_ORDER_CTA_CLICK]: undefined,
  [RFIDAnalyticsEvent.LINK_PAYMENT_CARD_SCREEN_OPEN]: undefined,
  [RFIDAnalyticsEvent.LINK_PAYMENT_CARD_SCREEN_CTA_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_OPEN]: undefined,
  [RFIDAnalyticsEvent.CONFIRM_AND_ORDER_OPEN]: undefined,
  [RFIDAnalyticsEvent.CHARGE_CARD_SCREEN_OPEN]: undefined,
  [RFIDAnalyticsEvent.CANCEL_CARD_SUCCESS]: () => {
    return logCustomEvent('rfid_cancel_card_success');
  },
  [RFIDAnalyticsEvent.POPULATED_SHIPPING_ADDRESS_OPEN]: undefined,
  [RFIDAnalyticsEvent.POPULATED_SHIPPING_ADDRESS_SAVE_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_BACK_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_USE_ADDRESS_CLICK]: undefined,
  [RFIDAnalyticsEvent.SHIPPING_ADDRESS_ERROR]: undefined,

  [AuthAnalyticsEventsType.CIP_REGISTRATION_ERROR]: undefined,
  [AuthAnalyticsEventsType.CIP_REGISTRATION_SUCCESS]: undefined,
  [AuthAnalyticsEventsType.CIP_LOGIN_SUCCESS]: undefined,
  [AuthAnalyticsEventsType.LOGIN_ERROR]: undefined,
  [AuthAnalyticsEventsType.LOGIN_SCREEN_CLOSE]: undefined,

  [LoginAnalyticsEvent.FIND_CHARGER_MAP_BANNER]: undefined,
  [LoginAnalyticsEvent.LOGIN_CLICK]: undefined,
  [LoginAnalyticsEvent.CREATE_ACCOUNT_CLICK]: undefined,
  [LoginAnalyticsEvent.CONFIRM_UBER_DRIVER_CLICK]: undefined,

  [OutageAnalyticsEvent.OUTAGE_FULL_OUTAGE_SCREEN_OPEN]: undefined,
  [OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_OPEN]: undefined,
  [OutageAnalyticsEvent.OUTAGE_PARTIAL_OUTAGE_BOTTOM_SHEET_CLOSE]: undefined,

  [TabsAnalyticsEvent.CHARGE_TAB_ACTIVE_CHARGE_CLICK]: undefined,
  [TabsAnalyticsEvent.CHARGE_TAB_NO_CHARGE_CLICK]: undefined,
  [TabsAnalyticsEvent.HELP_TAB_CLICK]: () => {
    return logCustomEvent('help_tab_click');
  },
  [TabsAnalyticsEvent.MAP_TAB_CLICK]: undefined,
  [TabsAnalyticsEvent.PROFILE_TAB_CLICK]: undefined,

  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CLOSE]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CONNECTOR_CHANGE]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_CONNECTOR_CONFIRM]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_START_CHARGE_CLICK]: async ({
    chargepoint,
    connector,
  }) => {
    try {
      const scheme = chargepoint?.schemes[0]?.schemeName;

      // Define event properties
      const properties = {
        CP_ID: chargepoint?.apolloInternalId || '',
        Site_Provider: chargepoint?.provider || '',
        Site_Country: chargepoint?.site?.siteDetails?.country || '',
        ConnectorType: connector?.type || '',
        CP_Scheme: scheme || '',
      };

      logCustomEvent('charge_start_screen_start_charge_click', properties);
    } catch (error) {
      logger.warn('Error logging Charge_StartChargeSuccessful event: ', error);
    }
  },
  [ChargeAnalyticsEvent.CHARGE_CONNECTING_SCREEN_OPEN]: async ({
    chargepoint,
    connector,
  }) => {
    try {
      const scheme = chargepoint?.schemes[0]?.schemeName;
      const eventName = 'charge_charge_connecting_screen_open';

      // Define event properties
      const properties = {
        CP_ID: chargepoint?.apolloInternalId || '',
        Site_Provider: chargepoint?.provider || '',
        Site_Country: chargepoint?.site?.siteDetails?.country || '',
        ConnectorType: connector?.type || '',
        CP_Scheme: scheme || '',
      };

      // Log the custom event
      await logCustomEvent(eventName, properties);
    } catch (error) {
      logger.warn(
        'Error logging Charge_ChargeConnectingScreen_Open event: ',
        error,
      );
    }
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_REQUEST_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_REQUEST_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ACCEPTED_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ACCEPTED_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_SUCCESSFUL]: async ({
    chargepoint,
    connector,
  }) => {
    try {
      const scheme = chargepoint?.schemes[0]?.schemeName;
      const eventName = 'charge_start_charge_successful';

      // Define event properties
      const properties = {
        CP_ID: chargepoint?.apolloInternalId || '',
        Site_Provider: chargepoint?.provider || '',
        Site_Country: chargepoint?.site?.siteDetails?.country || '',
        ConnectorType: connector?.type || '',
        CP_Scheme: scheme || '',
      };

      // Log the custom event
      await logCustomEvent(eventName, properties);
    } catch (error) {
      logger.warn('Error logging Charge_StartChargeSuccessful event: ', error);
    }
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_RESPONSE_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_LONG_WAIT_CALL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_UNSUCCESSFUL]: async ({
    chargepoint,
    connector,
  }) => {
    try {
      const scheme = chargepoint?.schemes[0]?.schemeName;
      const eventName = 'charge_start_charge_unsuccessful';
      const timestamp = new Date().toISOString();

      // Define event properties
      const properties = {
        CP_ID: chargepoint?.apolloInternalId || '',
        Site_Provider: chargepoint?.provider || '',
        Site_Country: chargepoint?.site?.siteDetails?.country || '',
        ConnectorType: connector?.type || '',
        CP_Scheme: scheme || '',
        timestamp: timestamp,
      };

      // Log the custom event
      await logCustomEvent(eventName, properties);
    } catch (error) {
      logger.error('Error handling charge failure:', error);
    }
  },
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_TRY_AGAIN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_GO_BACK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CONNECTING_RENEWABLE_EXPANDER_OPEN]: undefined,
  [ChargeAnalyticsEvent.NO_ACTIVE_SESSION_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.NO_ACTIVE_SESSION_SCREEN_FIND_A_CHARGER_SELECT]:
    undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_STOP_CHARGE_STEPS_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGING_POP_UP_YES_CLICK]: () => {
    return logCustomEvent('charge_stop_charging_popup_yes_click');
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGING_POP_UP_NO_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_DISCONNECTING_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_REQUEST_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_REQUEST_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_ACCEPTED_SUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_ACCEPTED_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT_WAIT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_LONG_WAIT_DISC]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT_DISC]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_APP_TIMEOUT_CALL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_SUCCESSFUL]: async ({
    chargepoint,
    connector,
  }) => {
    try {
      // Extract relevant details
      const cpo = chargepoint?.site?.cpo || '';
      const scheme = chargepoint?.schemes[0]?.schemeName || '';

      // Define the custom event name
      const eventName = 'charge_stopcharge_successful';

      // Create the event properties
      const properties = {
        CP_ID: chargepoint?.apolloInternalId || '',
        Site_Provider: chargepoint?.provider || '',
        Site_Country: chargepoint?.site?.siteDetails?.country || '',
        ConnectorType: connector?.type || '',
        CP_Scheme: scheme,
        CP_Operator: cpo,
      };

      // Track the custom event with Airship
      await logCustomEvent(eventName, properties);
    } catch (error) {
      logger.warn('Error logging Charge_StopChargeSuccessful event: ', error);
    }
  },

  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_RESPONSE_UNSUCCESSFUL]: undefined,
  [ChargeAnalyticsEvent.CHARGE_DISCONNECTING_TIMEOUT_OPEN]: () => {
    return logCustomEvent('charge_disconnecting_timeout_open');
  },
  [ChargeAnalyticsEvent.CHARGE_STOP_CHARGE_UNSUCCESSFUL]: () => {
    return logCustomEvent('charge_stopcharge_unsuccessful');
  },
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_OPEN]: async ({
    chargepoint,
    connector,
  }) => {
    try {
      // Extract relevant details
      const scheme = chargepoint?.schemes[0]?.schemeName || '';
      const cpo = chargepoint?.site?.cpo || '';

      // Define the custom event name
      const eventName = 'charge_summary_screen_open';

      // Create the event properties
      const properties = {
        CP_ID: chargepoint?.apolloInternalId || '',
        Site_Provider: chargepoint?.provider || '',
        Site_Country: chargepoint?.site?.siteDetails?.country || '',
        ConnectorType: connector?.type || '',
        CP_Scheme: scheme,
        CP_Operator: cpo,
      };

      await logCustomEvent(eventName, properties);
    } catch (error) {
      logger.warn('Error logging Charge_SummaryScreen_Open event: ', error);
    }
  },

  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_VAT_INVOICE_DOWNLOAD]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_DISCONNECT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_ERROR_CALL_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_NO_DATA_RETRY_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_NO_DATA_END_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_SCREEN_VAT_IN_HISTORY_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SUMMARY_ERROR_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_GUEST_START_CHARGE_SELECT_PAYMENT]: undefined,
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_SCREEN_CLOSE]: undefined,
  [ChargeAnalyticsEvent.CHARGE_PAYMENT_REQUIRED_HISTORY_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CHARGER_NO_LONGER_AVAILABLE_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_CHARGE_BPCM_TIMEOUT_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_START_SCREEN_PREAUTH_CLICK]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_SCREEN_OPEN]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_ID_MATCH]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_NO_ID_MATCH]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_NO_ACCESS]: undefined,
  [ChargeAnalyticsEvent.SERIAL_SEARCH_CHARGER_ID_HELP_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_NEAR_BY_SITE_SELECT_LOCATION_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_NEAR_BY_SITE_CHARGE_NOW_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_STOP_EXTERNAL_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CONNECT_INSTRUCTIONS_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_CONNECT_START_CHARGE_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_MAP_ERROR]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_NO_ACCESS]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_TOP_UP_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_NAVIGATE_TO_WEBSHOP]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_OUTSIDE_REGION]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_CHARGE_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_ADD_PAYMENT_METHOD]: undefined,
  [ChargeAnalyticsEvent.CHARGE_MONITORING_GET_CHARGE_NO_DATA]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SERVICE_UNAVAILABLE_OPEN]: undefined,
  [ChargeAnalyticsEvent.CHARGE_TOP_UP_SCREEN_TOP_UP_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_GET_DIRECTIONS_CLICK]: undefined,
  [ChargeAnalyticsEvent.CHARGE_SELECT_CONNECTOR_CHARGER_INFORMATION_CLICK]:
    undefined,

  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_OPEN]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_BACK]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_KEEP_CLICK]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_DELETE_CLICK]: undefined,
  [RTBFAnalyticsEvent.DELETE_ACCOUNT_SCREEN_SETTINGS_CLICK]: undefined,

  [WalletAnalyticsEvent.CHANGE_DEFAULT_CARD_SAVE]: undefined,
  [WalletAnalyticsEvent.REMOVE_CARD_CANT_REMOVE_POP_UP_OPEN]: undefined,
  [WalletAnalyticsEvent.REMOVE_CARD_ERROR_POP_UP_OPEN]: undefined,
  [WalletAnalyticsEvent.REMOVE_FINAL_CARD_POP_UP_REMOVE_CLICK]: undefined,
  [WalletAnalyticsEvent.REMOVE_CARD_POP_UP_REMOVE_CLICK]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_FAILED_SCREEN_CLOSE]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_FAILED_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_SUCCESS_SCREEN_FINISH]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_SUCCESS_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_SUM_SCREEN_AGREE_CLICK]: undefined,
  [WalletAnalyticsEvent.OVERDRAWN_BANNER_VIEW_CLICK]: undefined,
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_CLOSE_CLICK]: undefined,
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_RETRY_CLICK]: undefined,
  [WalletAnalyticsEvent.UNABLE_TO_ADD_CARD_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.CARD_ADDED_SCREEN_FINISH_CLICK]: undefined,
  [WalletAnalyticsEvent.CARD_ADDED_SCREEN_OPEN]: undefined,
  [WalletAnalyticsEvent.PAYMENT_DETAILS_ADD_NEW_CARD_CLICK]: undefined,
  [WalletAnalyticsEvent.PAYMENT_DETAILS_MY_SUBS_CLICK]: undefined,
  [WalletAnalyticsEvent.CHARGE_DEFAULT_CARD_ADD_NEW]: undefined,
  [WalletAnalyticsEvent.CHARGE_DEFAULT_CARD_UPDATE]: undefined,
  [WalletAnalyticsEvent.PAYMENT_DETAILS_OPEN]: undefined,
  [WalletAnalyticsEvent.PAYMENT_SCREEN_RECORD_DETECTED_OPEN]: undefined,
  [WalletAnalyticsEvent.ADD_CARD_SCREEN_RECORD_DETECTED_OPEN]: undefined,
  [WalletAnalyticsEvent.EXPOSED_COMPONENT_ERROR_OPEN]: undefined,
  [WalletAnalyticsEvent.EXPOSED_COMPONENT_ERROR_RETRY_CLICK]: undefined,

  [CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_OPEN]: undefined,
  [CreditAnalyticsEvent.CREDIT_STRIPE_WEBVIEW_CLOSED]: undefined,
  [CreditAnalyticsEvent.CREDIT_YOUR_CREDIT_SCREEN_ERROR_VIEW]: undefined,
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_BUTTON_CLICK]: undefined,
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_SUCCESS]: undefined,
  [CreditAnalyticsEvent.CREDIT_TOP_UP_CREDIT_FAILED]: undefined,
  [CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_SUCCESS]: undefined,
  [CreditAnalyticsEvent.CREDIT_GET_CUSTOMER_ERROR]: undefined,

  [SubsAnalyticsEvent.SUBS_INTRO_OFFER_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_REACTIVATE_SUBS_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_RFID_PREFERENCE_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_ENTER_ADDRESS_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_SUCCESS]: undefined,
  [SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_ERROR]: undefined,
  [SubsAnalyticsEvent.SUBS_SETUP_DIRECT_DEBIT_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_SETUP_COMPLETE]: () =>
    logCustomEvent('subs_setup_complete'),
  [SubsAnalyticsEvent.SUBS_PENDING_SCREEN_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_COMPLETE]: () =>
    logCustomEvent('subs_cancel_subscription_complete'),
  [SubsAnalyticsEvent.SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK]: undefined,
  [SubsAnalyticsEvent.SUBS_UPGRADE_PAYMENTREQUIRED_OPEN]: undefined,
  [SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_FAILED]: undefined,

  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_EXIT_CLICK]: undefined,
  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_OPEN]: undefined,
  [RegAnalyticsEvent.ADD_EMAIL_ADDRESS_SEND_VERIF_CLICK]: undefined,
  [RegAnalyticsEvent.CUSTOMISE_BP_PULSE_APP_EXIT_CLICK]: undefined,
  [RegAnalyticsEvent.CUSTOMISE_BP_PULSE_APP_OPEN]: undefined,
  [RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_CONTINUE_CLICK]: undefined,
  [RegAnalyticsEvent.EMAIL_ADDRESS_VERIFIED_OPEN]: undefined,
  [RegAnalyticsEvent.EMAIL_VER_SCREEN_CHANGE_EMAIL_CLICK]: undefined,
  [RegAnalyticsEvent.EMAIL_VER_SCREEN_VERIFIED_EMAIL_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_BACK_TOBPPULSE_APP_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_POP_UP_CONTINUE_SETUP_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_POP_UP_LOG_OUT_CLICK]: undefined,
  [RegAnalyticsEvent.EXIT_POP_UP_OPEN]: undefined,
  [RegAnalyticsEvent.INCORRECT_ACCESS_TOKEN_OPEN]: undefined,
  [RegAnalyticsEvent.JUMP_RIGHT_IN_CLICK]: undefined,
  [RegAnalyticsEvent.MARKETING_OPT_IN_SAVE]: undefined,
  [RegAnalyticsEvent.MARKETING_OPT_OUT_SAVE]: undefined,
  [RegAnalyticsEvent.SETTING_UP_EV_ACCOUNT_LOGOUT_CLICK]: undefined,
  [RegAnalyticsEvent.SETTING_UP_EV_ACCOUNT_TRY_AGAIN_CLICK]: undefined,
  [RegAnalyticsEvent.SETTING_UP_EVACCOUNT_OPEN]: undefined,
  [RegAnalyticsEvent.TRY_AGAIN_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_GO_BACK_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_LOGIN_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_SAVE_DETAILS_ERROR_OPEN]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_CLOSE_CLICK]: undefined,
  [RegAnalyticsEvent.UNABLE_TO_VERIFY_EMAIL_OPEN]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_CLOSE_CLICK]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_OPEN]: undefined,
  [RegAnalyticsEvent.UPDATE_EMAIL_ADDRESS_SEND_VERIF_CLICK]: undefined,
  [RegAnalyticsEvent.VERIFICATION_LINK_SENT_EXIT_CLICK]: undefined,
  [RegAnalyticsEvent.VERIFICATION_LINK_SENT_OPEN]: undefined,
  [RegAnalyticsEvent.EV_ACCOUNT_SETUP_SUCCESSFUL]: undefined,
  [RegAnalyticsEvent.EV_ACCOUNT_SETUP_UNSUCCESSFUL]: undefined,

  [FavouritesAnalyticsEvent.GET_FAVOURITES_SUCCESS]: undefined,
  [FavouritesAnalyticsEvent.GET_FAVOURITES_FAILED]: undefined,
  [FavouritesAnalyticsEvent.ADD_FAVOURITE_SUCCESS]: () => {
    return logCustomEvent('favourites_addfavourite_success');
  },
  [FavouritesAnalyticsEvent.ADD_FAVOURITE_FAILED]: undefined,
  [FavouritesAnalyticsEvent.REMOVE_FAVOURITE_SUCCESS]: undefined,
  [FavouritesAnalyticsEvent.REMOVE_FAVOURITE_FAILED]: undefined,

  [OnboardingAnalyticsEvent.REQUEST_SUCCESSFUL]: undefined,
  [OnboardingAnalyticsEvent.REQUEST_UNSUCCESSFUL]: undefined,

  [UberAnalyticsEvent.UNLINK_ACCOUNTS_MODAL_OPEN]: undefined,
  [UberAnalyticsEvent.WELCOME_TO_UBER_OPEN]: undefined,
  [UberAnalyticsEvent.WELCOME_TO_UBER_FIND_NEARBY_CHARGER_CLICK]: undefined,
  [UberAnalyticsEvent.WELCOME_TO_UBER_VIEW_REWARDS_CLICK]: undefined,
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_OPEN]: undefined,
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_VERIFY_CLICK]: undefined,
  [UberAnalyticsEvent.REAUTH_UBER_LINK_EXPIRED_X_CLICK]: undefined,
  [UberAnalyticsEvent.LOGIN_WITH_UBER_SUCCESS]: undefined,
  [UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_SUBSCRIBE_CLICK]: undefined,
  [UberAnalyticsEvent.UBER_UNLINK_ACCOUNT_SUCCESS_FIND_CHARGER_CLICK]:
    undefined,
  [UberAnalyticsEvent.UNLINK_ACCOUNTS_KEEP_UBER_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINK_ACCOUNTS_DISCONNECT_ACCOUNTS_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_EVENT]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_OPEN]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_CONTACT_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINKING_UNSUCCESSFUL_TRY_AGAIN_CLICK]: undefined,
  [UberAnalyticsEvent.UNLINKING_ACCOUNTS_OPEN]: undefined,
  [UberAnalyticsEvent.UNLINING_ACCOUNTS_SUCCESSFUL_OPEN]: undefined,
  [UberAnalyticsEvent.CONNECTING_ACCOUNTS_SUCCESSFUL_OPEN]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_OPEN]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_TRYAGAIN_CLICK]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_UNLINK_CLICK]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRYLIMIT_OPEN]: undefined,
  [UberAnalyticsEvent.UBER_INELIGIBLE_ACCOUNT_RETRY_SUCCESS]: undefined,

  [SiteBannerAnalyticsEvent.CHARGE_SELECT_CONNECTOR_UNENTITLED]: undefined,
  [SiteBannerAnalyticsEvent.MAP_SITE_UNENTITLED]: undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSCRIPTIONINVOICESTAB_CLICK]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_DOWNLOADVAT_CLICK]:
    undefined,
  [ChargeHistoryAnalyticsEvent.HISTORY_SUBSINVOICE_DOWNLOADVAT_FAILED]:
    undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_BACK_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_KEEP_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_CONFIRM_CLICK]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_OPEN]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_KEEP_ACCOUNT_CLICK]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_CANCEL_SUBS_CLICK]: undefined,
  [RTBFAnalyticsEvent.GUIDANCE_SCREEN_NEXT_CLICK]: undefined,
  [RTBFAnalyticsEvent.SELECT_OPTION_SELECT_CLICK]: undefined,
  [RTBFAnalyticsEvent.SELECT_OPTION_PREVIOUS_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRMATION_CONFIRM_CLICK]: undefined,
  [RTBFAnalyticsEvent.CONFIRMATION_PREVIOUS_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_KEEP_ACCOUNT_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_CANCEL_SUBS_CLICK]: undefined,
  [RTBFAnalyticsEvent.REASON_SCREEN_MANAGE_MARKETING_CLICK]: undefined,
  [RTBFAnalyticsEvent.REQUEST_SENT_OPEN]: undefined,
  [RTBFAnalyticsEvent.REQUEST_FAILED_OPEN]: undefined,
  [RTBFAnalyticsEvent.GET_IN_TOUCH_CLICK]: undefined,
  [RTBFAnalyticsEvent.REFUND_EMAIL_SENT]: undefined,
};

interface AirshipConfig {
  appSecret: string;
  appKey: string;
  site?: 'us' | 'eu';
  urlAllowList?: string[];
  notificationIcon?: string;
  accentColor?: string;
  inProduction?: boolean;
}

// Define an interface for deeplink handlers
interface DeeplinkMapping {
  path: string;
  handler: (params?: URLSearchParams) => void;
}

export class AirshipAnalyticsService {
  private static isInitialized = false;
  private static isReady = false;
  private static eventListenersAdded = false;
  private static initPromise: Promise<boolean> | null = null;

  // Centralized registry of deeplink mappings
  private static deeplinkMappings: DeeplinkMapping[] = [
    {
      path: 'airshipMap',
      handler: () => navigate('Tabs', { screen: 'Map' }, true),
    },
    {
      path: 'profile_marketing_preferences',
      handler: () => navigate('Tabs', { screen: 'Profile' }, true),
    },
    {
      path: 'rfid',
      handler: () => navigate('RFID', { screen: RFIDLinkingConfig }, true),
    },
    {
      path: 'profile_transaction',
      handler: () =>
        navigate('ChargeActivity', { screen: 'ChargeActivity' }, true),
    },
    {
      path: 'map',
      handler: () => navigate('Tabs', { screen: 'Map' }, true),
    },
    {
      path: 'subscribe_now',
      handler: () => navigate('Subscription', { screen: 'Subscription' }, true),
    },
  ];
  /**
   * Handles logging a custom analytics event to Airship
   * @param {AnalyticsEventType} event an analytics event
   */
  public static analyticsEvent = async (event: AnalyticsEventType) => {
    const command: Function | undefined = eventMap[event.type];
    if (command) {
      return command(event.payload);
    }
    return null;
  };

  public static createCustomEvent = (
    eventName: string,
    customProperties: { [key: string]: any } = {},
    userInfo: { [key: string]: any } = {},
  ): CustomEvent => {
    const properties: { [key: string]: any } = {
      ...customProperties,
      userInfo,
    };

    return {
      eventName,
      properties,
    };
  };

  private static createAirshipConfig(): AirshipConfig {
    return {
      appSecret: env.AIRSHIP_APP_SECRET,
      appKey: env.AIRSHIP_APP_KEY,
      site: env.AIRSHIP_SITE as 'us' | 'eu' | undefined,
      urlAllowList: ['*'],
      notificationIcon: 'ic_notification',
      accentColor: '#00ff00',
      inProduction:
        env.AIRSHIP_IN_PRODUCTION !== undefined
          ? env.AIRSHIP_IN_PRODUCTION
          : false,
    };
  }

  private static getTakeOffConfig(config: AirshipConfig) {
    return {
      default: {
        appSecret: config.appSecret,
        appKey: config.appKey,
      },
      site: config.site || 'us',
      urlAllowList: config.urlAllowList || ['*'],
      inProduction: config.inProduction,
      android: {
        notificationConfig: {
          icon: config.notificationIcon || 'ic_notification',
          accentColor: config.accentColor || '#00ff00',
        },
      },
    };
  }

  private static async verifyAirshipFlying(resolve: (value: boolean) => void) {
    // Check if Airship is actually flying
    const isFlying = await Airship.isFlying();
    if (isFlying) {
      logger.info('Airship initialized and flying successfully.');
      this.isReady = true;
      this.checkAirshipContactAvailablity();
      resolve(true);
      return;
    }

    // If not flying, try again after a delay
    logger.warn('Airship initialized but not flying yet. Will try again...');
    setTimeout(async () => {
      const isNowFlying = await Airship.isFlying();
      logger.info(`Second isFlying check: ${isNowFlying}`);
      this.isReady = isNowFlying;

      if (isNowFlying) {
        this.checkAirshipContactAvailablity();
      }

      resolve(isNowFlying);
    }, 1000);
  }

  public static initAirShip = () => {
    // If already initializing, return the existing promise
    if (this.initPromise) {
      return this.initPromise;
    }

    // If already initialized and ready, return resolved promise
    if (this.isInitialized && this.isReady) {
      logger.info(
        'Airship already initialized and ready, skipping initialization.',
      );
      return Promise.resolve(true);
    }

    // Create initialization promise
    this.initPromise = new Promise<boolean>(async resolve => {
      try {
        const config = this.createAirshipConfig();
        await Airship.takeOff(this.getTakeOffConfig(config));
        this.isInitialized = true;

        // Register event listeners after takeOff to avoid race conditions
        this.addEventListeners();

        // Enable push notifications after initialization
        await this.enablePushNotifications();

        await this.verifyAirshipFlying(resolve);
      } catch (error) {
        logger.error('Error initializing Airship:', error);
        this.isInitialized = false;
        this.isReady = false;
        this.initPromise = null;
        resolve(false);
      }
    });

    return this.initPromise;
  };

  public static addDeeplinkMapping(
    path: string,
    handler: (params?: URLSearchParams) => void,
  ) {
    this.deeplinkMappings.push({ path, handler });
    logger.info(`Added deeplink mapping for path: ${path}`);
  }

  private static extractDeeplinkPath(deepLink: string): string | null {
    const match = deepLink.toString().match(/^.*:\/\/(.+)$/);
    return match ? match[1] : null;
  }

  private static findDeeplinkMapping(
    path: string | null,
  ): DeeplinkMapping | undefined {
    if (!path) {
      return undefined;
    }
    return AirshipAnalyticsService.deeplinkMappings.find(m => m.path === path);
  }

  private static handleDeepLink(deepLink: string) {
    try {
      logger.log('Processing deepLink:', deepLink);

      // Parse the URL to get the path and parameters
      const url = new URL(deepLink);

      // Extract the path and find the mapping
      const path = this.extractDeeplinkPath(deepLink);
      const mapping = this.findDeeplinkMapping(path);

      logger.log('Deeplink path:', path, 'Mapping found:', !!mapping);

      if (mapping) {
        logger.log(`Executing handler for deeplink path: ${path}`);
        mapping.handler(url.searchParams);
      } else {
        logger.warn(`No handler found for deeplink path: ${path}`);
      }
    } catch (error) {
      logger.error('Error handling deeplink:', error);
    }
  }

  private static addEventListeners() {
    // Only add event listeners once
    if (this.eventListenersAdded) {
      logger.info('Airship event listeners already added, skipping.');
      return;
    }

    Airship.addListener(EventType.DeepLink, event => {
      const deepLink = event.deepLink;
      logger.log('Received deepLink:', deepLink);
      this.handleDeepLink(deepLink);
    });

    Airship.addListener(EventType.NotificationResponse, event => {
      logger.log('NotificationResponse:', JSON.stringify(event));
    });

    Airship.addListener(EventType.PushReceived, event => {
      logger.log('PushReceived:', JSON.stringify(event));

      // Handle foreground notifications
      // Check if the push was received while app is in foreground
      const isForeground = (event as any).isForeground;
      if (isForeground) {
        logger.info('Received push notification in foreground');
        AirshipAnalyticsService.handleForegroundNotification(event);
      }
    });

    this.eventListenersAdded = true;
    logger.info('Airship event listeners added successfully.');
  }

  private static checkAirshipContactAvailablity() {
    // Ensure contact is accessible
    if (!Airship.contact) {
      logger.error('Airship.contact is undefined!');
      this.isReady = false;
    } else {
      logger.info('Airship.contact is available.');
    }
  }
  static async setCustomerTypeAttribute(customerType: string | undefined) {
    if (customerType) {
      try {
        Airship.channel
          .editAttributes()
          .setAttribute('customer_type', customerType);
        logger.debug('User Type : ', customerType);
      } catch (error) {
        logger.error('Error setting customer type attribute:', error);
      }
    }
  }
  static async associateUserIdToAirshipNamedUserId(userId: string) {
    try {
      await Airship.contact.identify(userId);
      const namedUserId = await Airship.contact.getNamedUserId();
      logger.debug('NAMED USED ID : ', namedUserId);
    } catch (error) {
      logger.error('Error while setting contact identity in Airship:', error);
    }
  }

  static async setSalesforcePushConsentAttribute(consentStatus: boolean) {
    try {
      await Airship.contact
        .editAttributes()
        .setAttribute('salesforce_push_consent', consentStatus)
        .apply();
      logger.info(
        'Salesforce Push consent attribute set successfully:',
        consentStatus,
      );
    } catch (error) {
      logger.error('Error setting Salesforce Push consent attribute:', error);
    }
  }

  /**
   * Enable push notifications and set up proper notification options
   */
  private static async enablePushNotifications() {
    try {
      // Enable user notifications
      await Airship.push.setUserNotificationsEnabled(true);
      logger.info('Push notifications enabled successfully');

      // Set iOS notification options
      if (Platform.OS === 'ios') {
        try {
          // Use the constants from Airship.iOS.NotificationOption
          await Airship.push.iOS.setNotificationOptions([
            'alert',
            'badge',
            'sound',
          ] as any); // Type assertion to bypass TypeScript issues
          logger.info('iOS notification options set successfully');
        } catch (iosError) {
          logger.warn('Failed to set iOS notification options:', iosError);
        }
      }

      // Log current notification status
      const status = await Airship.push.getNotificationStatus();
      logger.info('Current notification status:', JSON.stringify(status));
    } catch (error) {
      logger.error('Error enabling push notifications:', error);
    }
  }

  /**
   * Handle foreground notifications by displaying them to the user
   */
  private static handleForegroundNotification(event: any) {
    try {
      const pushPayload = event.pushPayload;
      const alert = pushPayload?.extras?.['com.urbanairship.push.ALERT'] || pushPayload?.alert;

      if (alert) {
        logger.info('Displaying foreground notification:', alert);

        // For now, we'll use a simple alert to display the notification
        // In a production app, you might want to use a custom notification component
        // or a library like react-native-flash-message
        if (Platform.OS === 'ios') {
          // On iOS, we can use the native alert
          import('react-native').then(({ Alert }) => {
            Alert.alert('Notification', alert);
          });
        } else {
          // On Android, we can also use Alert or implement a custom solution
          import('react-native').then(({ Alert }) => {
            Alert.alert('Notification', alert);
          });
        }
      } else {
        logger.warn('No alert text found in foreground notification');
      }
    } catch (error) {
      logger.error('Error handling foreground notification:', error);
    }
  }
}
