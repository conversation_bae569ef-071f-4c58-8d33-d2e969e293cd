import { WifiOff } from '@assets/images';
import { useCharge } from '@bp/charge-mfe';
import { SimpleNotification } from '@bp/ui-components/mobile/core';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Sound from 'react-native-sound';
import { Platform } from 'react-native';

import { useConnectivity } from '../../providers/ConnectivityProvider';

// Initialize sound
Sound.setCategory('Playback');

const Notifications = () => {
  const { isCharging: isChargingHook } = useCharge();
  const { isInternetReachable } = useConnectivity();
  const { t } = useTranslation();

  const [isConnected, setIsConnected] = useState(true);
  const [isCharging, setIsCharging] = useState(false);
  const [sound, setSound] = useState(null);

  // Initialize sound on component mount
  useEffect(() => {
    const soundPath = Platform.OS === 'ios' 
      ? 'charge_start.mp3' 
      : 'charge_start.mp3';
    
    const notificationSound = new Sound(soundPath, Sound.MAIN_BUNDLE, (error) => {
      if (error) {
        console.log('Failed to load sound', error);
      }
    });
    
    setSound(notificationSound);
    
    return () => {
      if (notificationSound) {
        notificationSound.release();
      }
    };
  }, []);

  useEffect(() => {
    if (isChargingHook !== isCharging) {
      setIsCharging(isChargingHook);
      
      // Play sound when charging starts
      if (isChargingHook && sound) {
        sound.play();
      }
    }

    if (isInternetReachable !== isConnected) {
      setIsConnected(isInternetReachable);
      
      // Play sound when connection status changes
      if (!isInternetReachable && sound) {
        sound.play();
      }
    }
  }, [isInternetReachable, isChargingHook, isCharging, isConnected, sound]);

  if (!isConnected && !isCharging) {
    return (
      <SimpleNotification
        title={t('notifications.lostConnection.title')}
        icon={<WifiOff />}
      />
    );
  }

  if (!isConnected && isCharging) {
    return (
      <SimpleNotification
        icon={<WifiOff />}
        title={t('notifications.lostConnection.title')}
        text={t('notifications.lostConnection.text')}
      />
    );
  }

  return null;
};

export default Notifications;
