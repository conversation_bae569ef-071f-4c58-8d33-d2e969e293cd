import { useEffect, useState, useCallback } from 'react';
import Sound from 'react-native-sound';
import { Platform } from 'react-native';

// Initialize sound category for playback
Sound.setCategory('Playback');

interface UseNotificationSoundOptions {
  soundFileName?: string;
  volume?: number;
  enabled?: boolean;
}

interface UseNotificationSoundReturn {
  playSound: () => void;
  isLoaded: boolean;
  isEnabled: boolean;
  setEnabled: (enabled: boolean) => void;
}

/**
 * Custom hook for playing notification sounds
 * @param options Configuration options for the sound
 * @returns Object with playSound function and loading state
 */
export const useNotificationSound = (
  options: UseNotificationSoundOptions = {}
): UseNotificationSoundReturn => {
  const {
    soundFileName = 'charge_start',
    volume = 0.8,
    enabled = true,
  } = options;

  const [sound, setSound] = useState<Sound | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isEnabled, setIsEnabled] = useState(enabled);

  // Play notification sound with error handling
  const playSound = useCallback(() => {
    if (sound && isLoaded && isEnabled) {
      sound.setVolume(volume);
      sound.play((success) => {
        if (!success) {
          console.warn('Failed to play notification sound');
        }
      });
    }
  }, [sound, isLoaded, isEnabled, volume]);

  // Initialize sound on hook mount
  useEffect(() => {
    let notificationSound: Sound | null = null;

    const initializeSound = () => {
      // Platform-specific sound loading
      const soundPath = Platform.OS === 'ios'
        ? `sounds/${soundFileName}.mp3` // iOS: file in app bundle Resources/sounds/
        : soundFileName; // Android: file in res/raw/ directory (no extension needed)

      const soundSource = Sound.MAIN_BUNDLE; // Both platforms use MAIN_BUNDLE

      notificationSound = new Sound(soundPath, soundSource, (error) => {
        if (error) {
          console.error('Failed to load notification sound:', error);
          console.error('Sound path:', soundPath);
          console.error('Platform:', Platform.OS);
          setIsLoaded(false);
          return;
        }

        console.log('Notification sound loaded successfully');
        console.log('Sound path:', soundPath);
        console.log('Platform:', Platform.OS);
        setIsLoaded(true);
        setSound(notificationSound);
      });
    };

    initializeSound();

    return () => {
      if (notificationSound) {
        notificationSound.release();
        setIsLoaded(false);
        setSound(null);
      }
    };
  }, [soundFileName]);

  const setEnabled = useCallback((enabled: boolean) => {
    setIsEnabled(enabled);
  }, []);

  return {
    playSound,
    isLoaded,
    isEnabled,
    setEnabled,
  };
};
